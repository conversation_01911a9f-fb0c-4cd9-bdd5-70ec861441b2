from fileinput import filename
import logging
import sys
from cv2 import merge

from selenium import webdriver
from selenium.webdriver.common.by import By
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.options import Options
import time
from lxml import etree
import glob
import os
import shutil
import variables as var
from svgelements import *

from selenium.webdriver.chrome.service import Service

import gen_merged_all_states
import threshold_tr_image_to_svg


# 120,120   1100
# 100,100   1000
# 100,120   1000
# 80,120    900
# 80,100    800
# 80,80     800
# 60,120   800
# 60,90    700
# 60,60    600
# 50,100   600


# import xml.etree.ElementTree as et

# python main_faster.py --img_path .\Input_image\testpics\1.png


def merge_svg_files(source_file, shapes_file, output_file="merged.svg"):
    shapes_tree = etree.parse(shapes_file)
    source_tree = etree.parse(source_file)

    valid_shape_tags = ["rect", "circle", "ellipse", "line", "polyline", "polygon", "path"]
    source_tree_elements = source_tree.iter()
    shape_elements = []
    for element in source_tree_elements:
        tag_split = element.tag.split("}")
        if len(tag_split) > 1 and tag_split[1] in valid_shape_tags:
            shape_elements.append(element)
    print(shape_elements)
    # shape_elements = list(filter(lambda element: element.tag.split("}") in valid_shape_tags, source_tree_elements))
    if len(shape_elements) == 0:
        logging.error("Source svg file does not contain any elements!")
        sys.exit(1)
    elif len(shape_elements) > 1:
        logging.error("Source svg file cannot contain more than one shape!")
        sys.exit(1)

    src_shape_element = shape_elements[0]
    src_shape_element.set("id", "src-node")
    # In case the svg file contains only one valid shape element
    shapes_tree.getroot().append(src_shape_element)
    etree.cleanup_namespaces(shapes_tree)
    shapes_tree.write(output_file)


def fix_output_style(output_file):
    print(output_file)
    output_tree = etree.parse(output_file)
    output_tree_elements = output_tree.iter()
    output_svg_elements = list(filter(lambda element: element.tag.split("}")[1] in ["svg"], output_tree_elements))
    if len(output_svg_elements) == 0:
        logging.error("Invalid output svg!")

    svg_element = output_svg_elements[0]
    svg_element.set("style", "fill: none; stroke: #000; stroke-width: 1px;")
    etree.cleanup_namespaces(output_tree)
    output_tree.write(output_file)


def ensure_output_dir_exists():
    if not os.path.exists(var.OUTPUTS_DIR):
        os.makedirs(var.OUTPUTS_DIR)
    else:
        shutil.rmtree(var.OUTPUTS_DIR)
        os.makedirs(var.OUTPUTS_DIR)



def execute_nesting(src_path, shapes_path, merged_path):
    chrome_options = Options()
    # Prevent browser from getting closed when main thread is finished
    chrome_options.add_experimental_option("detach", True)
    # Allow chrome to access local files and load web workers when accessing local files
    chrome_options.add_argument("--allow-file-access-from-files")
    """To open browser in silent mode"""
    chrome_options.headless = var.SILENT_MODE
    driver = webdriver.Chrome(ChromeDriverManager().install(), chrome_options=chrome_options)
    driver.get(f"file://{var.CURRENT_DIR}/SVGnest/index.html")
    # driver.execute_script("document.body.style.zoom='50%'")
    choose_file = driver.find_element(By.ID, "fileinput")
    # merge_svg_files(src_path, shapes_path)
    
    # choose_file.send_keys(os.path.join(var.CURRENT_DIR, "merged.svg"))         
    choose_file.send_keys(merged_path)

    source_node_element = driver.find_element(By.ID, "src-node")
    print(source_node_element)
    source_node_element.click()
    time.sleep(1)
    start_nest_button = driver.find_element(By.ID, "start")
    start_nest_button.click()

    available_svgnest_outputs = glob.glob(os.path.join(var.DOWNLOADS_PATH, "SVGnest*.svg"))
    for file in available_svgnest_outputs:
        os.remove(file)

    while True:
        download_button = driver.find_element(By.ID, "download")
        download_button_classes = download_button.get_attribute("class").split(" ")
        # extract_metrics
        material_utilization = driver.find_element(By.ID, "info_efficiency").get_attribute("innerHTML")
        iterations_count = driver.find_element(By.ID, "info_iterations").get_attribute("innerHTML")
        placed_parts = driver.find_element(By.ID, "info_placed").get_attribute("innerHTML")
        print(f"Utilization: {material_utilization}")
        print(f"Iterations: {iterations_count}")
        print(f"Parts Placed: {placed_parts}")
        try:
            if int(iterations_count) >= var.ITERATIONS:
                download_button.click()
                break
            else:
                download_button.click()
                while not os.path.exists(os.path.join(var.DOWNLOADS_PATH, "SVGnest-output.svg")):
                    print("waiting for file...")
                chack_output_file_for_number_of_bins(os.path.join(var.DOWNLOADS_PATH, "SVGnest-output.svg"))
                os.remove(os.path.join(var.DOWNLOADS_PATH, "SVGnest-output.svg"))
        except Exception as e:
            pass
        # time.sleep(0.1)

    while not os.path.exists(os.path.join(var.DOWNLOADS_PATH, "SVGnest-output.svg")):
        pass

    # filename = os.path.basename(src_path)
    filename = ".svg"
    os.rename(os.path.join(var.DOWNLOADS_PATH, "SVGnest-output.svg"), os.path.join(var.DOWNLOADS_PATH, f"SVGnest-output-{filename}"))
    shutil.move(os.path.join(var.DOWNLOADS_PATH, f"SVGnest-output-{filename}"), var.OUTPUTS_DIR)
    fix_output_style(os.path.join(var.OUTPUTS_DIR, f"SVGnest-output-{filename}"))
    chack_output_file_for_number_of_bins(os.path.join(var.OUTPUTS_DIR, f"SVGnest-output-{filename}"))
    driver.close()
    driver.quit()



def start_chrome():
    global driver
    chrome_options = Options()
    # Prevent browser from getting closed when main thread is finished
    chrome_options.add_experimental_option("detach", True)
    # Allow chrome to access local files and load web workers when accessing local files
    chrome_options.add_argument("--allow-file-access-from-files")
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    """To open browser in silent mode"""
    chrome_options.headless = var.SILENT_MODE
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
    driver.get(f"file://{var.CURRENT_DIR}/SVGnest/index.html")
    # driver.execute_script("document.body.style.zoom='50%'")




def execute_nesting_on_merged_file_mine(merged_path):
    terminated_flag = 0
    not_fitable = 0
    
    filename = os.path.basename(merged_path)

    # driver.execute_script("document.body.style.zoom='50%'")
    choose_file = driver.find_element(By.ID, "fileinput")
    # merge_svg_files(src_path, shapes_path)
    
    # choose_file.send_keys(os.path.join(var.CURRENT_DIR, "merged.svg"))         
    choose_file.send_keys(merged_path)

    time.sleep(1)
    print(1)    

    source_node_element = driver.find_element(By.ID, "src-node")
    source_node_element.click()

    print(2)

    time.sleep(1)

    start_nest_button = driver.find_element(By.ID, "start")
    start_nest_button.click()

    print(3)
    time.sleep(1)

    available_svgnest_outputs = glob.glob(os.path.join(var.DOWNLOADS_PATH, "SVGnest*.svg"))
    for file in available_svgnest_outputs:
        os.remove(file)

    exit_button = driver.find_element(By.ID, "exitbutton")

    while True:
        download_button = driver.find_element(By.ID, "download")
        download_button_classes = download_button.get_attribute("class").split(" ")
        # extract_metrics
        material_utilization = driver.find_element(By.ID, "info_efficiency").get_attribute("innerHTML")
        iterations_count = driver.find_element(By.ID, "info_iterations").get_attribute("innerHTML")
        placed_parts = driver.find_element(By.ID, "info_placed").get_attribute("innerHTML")


        time_out = time.time() + 5
        while len(material_utilization)==0 or len(iterations_count)==0 or len(placed_parts)==0:
            print("waiy....................", time.time())
            material_utilization = driver.find_element(By.ID, "info_efficiency").get_attribute("innerHTML")
            iterations_count = driver.find_element(By.ID, "info_iterations").get_attribute("innerHTML")
            placed_parts = driver.find_element(By.ID, "info_placed").get_attribute("innerHTML")

            if time.time() > time_out:
                not_fitable = 1
                terminated_flag = 1
                break

            time.sleep(0.1)

        if not_fitable == 1:
            break
                
        if not not_fitable:

            print(f"Utilization: {material_utilization}")
            print(f"Iterations: {iterations_count}")
            print(f"Parts Placed: {placed_parts}")
            
            try:
                if int(iterations_count) == 1:
                    download_button.click()
                    time.sleep(1)
                    while not os.path.exists(os.path.join(var.DOWNLOADS_PATH, "SVGnest-output.svg")):
                        print("this_while_stucked_******************************************************")

                    available_svgnest_outputs = glob.glob(os.path.join(var.DOWNLOADS_PATH, "SVGnest*.svg"))
                    print("*/*/*/")
                    print(available_svgnest_outputs)
                    print("*/*/*/")
                    number_of_bins_used = chack_output_file_for_number_of_bins(os.path.join(var.DOWNLOADS_PATH, f"SVGnest-output.svg"))
                    print(number_of_bins_used)
                    print("/////////////////////////////////////////////////////////////////")
                    os.remove(os.path.join(var.DOWNLOADS_PATH, "SVGnest-output.svg"))
                    
                    if number_of_bins_used > 10:
                        print(f"{filename} terminated at {iterations_count}")
                        terminated_flag = 1
                        break
                    else:
                        terminated_flag = 0
                    # time.sleep(10)
                elif int(iterations_count) >= var.ITERATIONS:
                    if not_fitable == 0:
                        if placed_parts.split('/')[0] != placed_parts.split('/')[1]:
                            terminated_flag = 1
                            break
                        else:
                            download_button.click()
                            time.sleep(1)
                            break
                else:
                    pass
                    # download_button.click()
                    # while not os.path.exists(os.path.join(var.DOWNLOADS_PATH, "SVGnest-output.svg")):
                        # print("waiting for file...")
                    # chack_output_file_for_number_of_bins(os.path.join(var.DOWNLOADS_PATH, "SVGnest-output.svg"))
                    # os.remove(os.path.join(var.DOWNLOADS_PATH, "SVGnest-output.svg"))
            except Exception as e:
                print(e)
        # time.sleep(1)

    if terminated_flag == 0 and not_fitable == 0:

        while not os.path.exists(os.path.join(var.DOWNLOADS_PATH, "SVGnest-output.svg")):
            print("this_while_stucked")

        print("**********************")
        print("**********************")
        print("**********************")
        print(os.path.basename(merged_path))
        print("**********************")
        print("**********************")
        print("**********************")
        # filename = os.path.basename(src_path)
        # filename = ".svg"
        
        
        os.rename(os.path.join(var.DOWNLOADS_PATH, "SVGnest-output.svg"), os.path.join(var.DOWNLOADS_PATH, f"SVGnest-output-{filename}"))
        shutil.move(os.path.join(var.DOWNLOADS_PATH, f"SVGnest-output-{filename}"), var.OUTPUTS_DIR)
        fix_output_style(os.path.join(var.OUTPUTS_DIR, f"SVGnest-output-{filename}"))
        number_of_bins_used = chack_output_file_for_number_of_bins(os.path.join(var.OUTPUTS_DIR, f"SVGnest-output-{filename}"))
        os.rename(os.path.join(var.OUTPUTS_DIR, f"SVGnest-output-{filename}"),
                os.path.join(var.OUTPUTS_DIR, f"SVGnest-bin{number_of_bins_used}-output-{filename}"))




    exit_button.click()

    # driver.close()
    # driver.quit()



def execute_batch_operation():
    src_shapes = glob.glob(os.path.join(var.SRC_SHAPES_DIR, "*.svg"))
    print(src_shapes)
    # execute_nesting(src_shapes[0], "merged.svg")
    # print(var.SIZES_FILE)
    # execute_nesting(src_shapes[1], var.SIZES_FILE, "D:\YZDN_LAPTOP_PC_MEGA\Scanner_new_approach\svgnest-automation-just-one-sheet\shapes\merged_002.svg")
    # os.path.join(var.CURRENT_DIR, "merged.svg")

    # for src_shape in src_shapes:
    #     execute_nesting(src_shape, var.SIZES_FILE)
    # execute_nesting_on_merged_file_mine(os.path.join(var.CURRENT_DIR, src_shapes[0]))
    merged_files = glob.glob(os.path.join(var.MERGED_SVG_FOLDER_DIR, "*.svg"))
    print(merged_files)
    # execute_nesting_on_merged_file_mine(os.path.join(var.MERGED_SVG_FOLDER_DIR, ))


def execute_batch_operation_mine():
    merged_files = sorted(glob.glob(os.path.join(var.MERGED_SVG_FOLDER_DIR, "*.svg")))
    print(merged_files)
    for this_merged_svg in merged_files:
        print(this_merged_svg)
        this_merged_svg_abs_path = os.path.abspath(this_merged_svg)
        print(this_merged_svg_abs_path)
        execute_nesting_on_merged_file_mine(this_merged_svg_abs_path)


def execute_batch_operation_mine_single_file():
    this_merged_svg = "merged_SVG\<EMAIL>"
    print(this_merged_svg)
    this_merged_svg_abs_path = os.path.abspath(this_merged_svg)
    print(this_merged_svg_abs_path)
    execute_nesting_on_merged_file_mine(this_merged_svg_abs_path)


def chack_output_file_for_number_of_bins(output_file):
    print("**********************************************************")
    svg = SVG.parse(output_file)
    list_of_id = []
    for element in svg.elements():
        if isinstance(element, Path):
        # if isinstance(element, Polygon):
            # print(element.id)
            list_of_id.append(element.id)
    # print("num id :" + len(list_of_id))  
    for a in list_of_id:
        print(a)
    print(len(list_of_id))
    print("**********************************************************")

    return len(list_of_id)

if __name__ == '__main__':
    gen_merged_all_states

    print("**********************")
    print("**********************")
    print("**********************\n\n\n")

    merged_files = sorted(glob.glob(os.path.join(var.MERGED_SVG_FOLDER_DIR, "*.svg")))
    for this_merged_svg in merged_files:
        print(this_merged_svg)

    print("\n\n\n")

    print(len(merged_files), " states.\n")
    ans = input("start nesting? (yes/no)\n")

    if(ans == 'yes'):
    # print(var.DOWNLOADS_PATH)
        ensure_output_dir_exists()
        start_chrome()
    # execute_batch_operation()
        execute_batch_operation_mine()
        # execute_batch_operation_mine_single_file()
        driver.close()
        driver.quit()

    else:
        quit()

    