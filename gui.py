import sys
import cv2
from pathlib import Path  # ✅ Use pathlib for cross-platform paths
import subprocess
import time
from datetime import datetime
from PySide6.QtCore import Qt, QTimer, Signal, QProcess
from PySide6.QtWidgets import (
    QApplication,
    QLabel,
    QMainWindow,
    QPushButton,
    QVBoxLayout,
    QWidget,
    QFileDialog,
    QTextEdit,
    QScrollArea
)
from PySide6.QtGui import QPixmap, QImage, QTextCursor

# ✅ Helper function to get platform-independent absolute paths
def get_path(relative_path):
    return Path(relative_path).resolve()  # Converts to absolute path with correct OS separator



class StreamRedirector:
    """ Redirects stdout and stderr to a QTextEdit widget """
    
    def __init__(self, text_widget):
        self.text_widget = text_widget

    def write(self, message):
        """ Writes output to the text widget """
        self.text_widget.append(message.strip())  # Append new message
        self.text_widget.ensureCursorVisible()  # Scroll to latest line

    def flush(self):
        """ Flush method for compatibility """
        pass


class DragDropLabel(QLabel):
    """ Label that supports drag & drop image functionality """

    def __init__(self, update_file_path_callback):
        super().__init__("Drag & Drop Image Here")
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setStyleSheet("border: 2px dashed gray; padding: 20px;")
        self.setAcceptDrops(True)
        self.update_file_path_callback = update_file_path_callback  # Callback function to update file path

    def dragEnterEvent(self, event):
        if event.mimeData().hasUrls():
            event.acceptProposedAction()

    def dropEvent(self, event):
        urls = event.mimeData().urls()
        if urls:
            image_path = urls[0].toLocalFile()
            self.set_image(image_path)  
            self.update_file_path_callback(image_path)  # ✅ Update file path in SelectPage

    def set_image(self, image_path):
        """ Resizes dropped images to have a height of 500px while keeping aspect ratio """
        pixmap = QPixmap(image_path)
        if not pixmap.isNull():
            # Scale with a fixed height of 500px while keeping aspect ratio
            pixmap = pixmap.scaledToHeight(500, Qt.SmoothTransformation)
            self.setPixmap(pixmap)  # ✅ Display resized image


class SelectPage(QWidget):
    def __init__(self):
        super().__init__()
        self.webcam_window = None  
        self.file_path = None  

        self.page_layout = QVBoxLayout()
        self.select_btn = QPushButton("Select File")
        self.webcam_btn = QPushButton("Open Webcam")
        self.drag_drop = DragDropLabel(self.set_file_path) 
        self.status_output = QTextEdit('wainting for select image ...')
        self.status_output.setReadOnly(True)  # ✅ Prevent user input
        
        self.start_btn = QPushButton("Start Processing")
        self.start_btn.setEnabled(False)

        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setWidget(self.status_output)
        self.scroll_area.hide()  # ✅ Initially hide the scroll area

  

        self.select_btn.clicked.connect(self.open_file_dialog)
        self.webcam_btn.clicked.connect(self.open_webcam_page)
        self.start_btn.clicked.connect(self.start_processing)


        self.page_layout.addWidget(self.select_btn)
        self.page_layout.addWidget(self.webcam_btn)
        self.page_layout.addWidget(self.drag_drop)  
        self.page_layout.addWidget(self.start_btn)
        self.page_layout.addWidget(self.scroll_area)  # ✅ Add scrollable log area

        self.setLayout(self.page_layout)

    def start_proccess(self):
        print(self.file_path)

    def open_webcam_page(self):
        if not self.webcam_window or not self.webcam_window.isVisible():
            self.webcam_window = WebcamWindow()
            self.webcam_window.captured.connect(self.set_captured_image)  # ✅ Connect signal
            self.webcam_window.show()


    def set_captured_image(self, pixmap, file_path):
        """ Displays captured image and updates the file path """
        self.drag_drop.setPixmap(pixmap)  # ✅ Show image
        self.file_path = file_path  # ✅ Save file path
        self.start_btn.setEnabled(True)
        self.status_output.setText("Status: Image selected! \nPress on start process to start nesting.")  # ✅ Update status
        print(f"Captured Image Path: {self.file_path}")

    def set_file_path(self, file_path):
        """ Updates the file path when a file is selected or dropped """
        self.file_path = str(get_path(file_path))  # ✅ Ensure absolute, OS-friendly path
        self.start_btn.setEnabled(True)
        self.status_output.setText(f"Status: Image selected! \nPress 'Start Processing' to begin.")

    def open_file_dialog(self):
        """ Opens file dialog, resizes selected image to have a height of 500px, and updates UI """
        file_name, _ = QFileDialog.getOpenFileName(self, "Open File", "", "Images (*.png *.xpm *.jpg *.jpeg *.bmp)")
        if file_name:
            pixmap = QPixmap(file_name)
            if not pixmap.isNull():
                # Resize image to have a height of 500px while keeping aspect ratio
                pixmap = pixmap.scaledToHeight(500, Qt.SmoothTransformation)
                self.drag_drop.setPixmap(pixmap)  # ✅ Show resized image
                self.set_file_path(file_name)  # ✅ Store file path

    def start_processing(self):
        """ Starts processing and removes everything except the scrollable log output """
        if not self.file_path:
            self.status_output.append("Status: No image selected!")  # ✅ Append log
            return

        # ✅ Hide all elements except the scrollable log output
        self.select_btn.hide()
        self.webcam_btn.hide()
        self.start_btn.hide()
        self.drag_drop.hide()  

        self.scroll_area.show()  # ✅ Show scroll area only after clicking start process

        self.status_output.append("Processing started...")  # ✅ Append log

        
        # ✅ Resize scroll area
        self.scroll_area.resize(500, 500)

        # ✅ Adjust main window to fit the scroll area precisely
        parent_window = self.window()  # Get main window reference
        parent_window.adjustSize()  # Resize window to fit contents

        # ✅ Ensure the window has an exact size of 500x500 + margins
        margins = parent_window.contentsMargins()
        frame_geometry = parent_window.frameGeometry()
        extra_width = frame_geometry.width() - parent_window.width() + margins.left() + margins.right()
        extra_height = frame_geometry.height() - parent_window.height() + margins.top() + margins.bottom()

        parent_window.resize(500 + extra_width, 500 + extra_height)
        print('file_path___________________________', self.file_path)


        # Update img_path in threshold_tr_image_to_svg.py
        try:
            # Read the threshold_tr_image_to_svg.py file with UTF-8 encoding
            with open("threshold_tr_image_to_svg.py", "r", encoding="utf-8") as f:
                lines = f.readlines()

            # Escape backslashes for Windows paths
            escaped_path = self.file_path.replace("\\", "\\\\")
            with open("threshold_tr_image_to_svg.py", "w", encoding="utf-8") as f:
                for line in lines:
                    if line.strip().startswith("img_path"):
                        f.write(f'img_path = "{escaped_path}"\n')
                    elif line.strip().startswith("img = cv2.imread("):
                        f.write(f'img = cv2.imread("{escaped_path}")\n')
                    else:
                        f.write(line)

            # Start the process using QProcess
            self.process = QProcess(self)
            self.process.readyReadStandardOutput.connect(self.read_output)
            self.process.readyReadStandardError.connect(self.read_error)
            self.process.finished.connect(self.process_finished)

            python_cmd = "python3" if sys.platform != "win32" else "python"
            #self.process.start(python_cmd, [str(get_path("main_faster.py"))])
            self.process.start(python_cmd, [str(get_path("threshold_tr_image_to_svg.py"))])

        except Exception as e:
            self.status_output.append(f"Error: {e}")

    def read_output(self):
        """ Reads the standard output from the process and updates the GUI """
        output = self.process.readAllStandardOutput().data().decode()
        self.status_output.append(output.strip())  # ✅ Append logs

        # ✅ Scroll to the bottom automatically
        self.status_output.moveCursor(QTextCursor.MoveOperation.End)

    def read_error(self):
        """ Reads the error output from the process and updates the GUI """
        error = self.process.readAllStandardError().data().decode()
        if error:
            self.status_output.append(f"Error: {error.strip()}")

    def process_finished(self):
        """ Called when the process finishes execution """
        self.status_output.append("Processing completed!")  # ✅ Final update
        self.status_output.append("Threshold processing finished successfully!")

        # Automatically run SVG generation after threshold processing
        self.status_output.append("Starting SVG generation...")
        try:
            # Start the SVG generation process
            self.svg_process = QProcess(self)
            self.svg_process.readyReadStandardOutput.connect(self.read_svg_output)
            self.svg_process.readyReadStandardError.connect(self.read_svg_error)
            self.svg_process.finished.connect(self.svg_process_finished)

            python_cmd = "python3" if sys.platform != "win32" else "python"
            self.svg_process.start(python_cmd, [str(get_path("image_to_svg_bin.py"))])

        except Exception as e:
            self.status_output.append(f"Error starting SVG generation: {e}")

    def read_svg_output(self):
        """ Reads the standard output from the SVG generation process """
        output = self.svg_process.readAllStandardOutput().data().decode()
        self.status_output.append(output.strip())
        self.status_output.moveCursor(QTextCursor.MoveOperation.End)

    def read_svg_error(self):
        """ Reads the error output from the SVG generation process """
        error = self.svg_process.readAllStandardError().data().decode()
        if error:
            self.status_output.append(f"SVG Generation Error: {error.strip()}")

    def svg_process_finished(self):
        """ Called when the SVG generation process finishes """
        self.status_output.append("SVG generation completed!")
        self.status_output.append("✅ Files created: path.svg and path_parts_only.svg")
        self.status_output.append("🚀 Ready for SVGnest! Open SVGnest and upload path.svg")
        self.status_output.append("💡 In SVGnest: Select the blue rectangle as bin, black shape will be nested")




class WebcamWindow(QWidget):
    captured = Signal(QPixmap, str)  # ✅ Accepts both QPixmap and file path

    def __init__(self):
        super().__init__()
        self.capture = None
        self.timer = None

        layout = QVBoxLayout()

        self.video_label = QLabel("Webcam Feed")
        self.video_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.capture_button = QPushButton("Capture Image")
        self.capture_button.clicked.connect(self.capture_image)

        self.back_button = QPushButton("Back to Main Page")
        self.back_button.clicked.connect(self.go_back)

        layout.addWidget(self.video_label)
        layout.addWidget(self.capture_button)
        layout.addWidget(self.back_button)

        self.setLayout(layout)

        self.start_webcam()

    def start_webcam(self):
        self.capture = cv2.VideoCapture(0)
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_frame)
        self.timer.start(30)

    def update_frame(self):
        ret, frame = self.capture.read()
        if ret:
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            h, w, ch = frame.shape
            bytes_per_line = ch * w
            q_img = QImage(frame.data, w, h, bytes_per_line, QImage.Format.Format_RGB888)
            self.video_label.setPixmap(QPixmap.fromImage(q_img))

    def capture_image(self):
        """ Captures an image, saves it with a unique name, and properly closes the webcam window """
        ret, frame = self.capture.read()
        if ret:
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # Get aspect ratio and resize while keeping width proportional
            h, w, ch = frame.shape
            new_width = int((500 / h) * w)  # Scale width proportionally
            frame = cv2.resize(frame, (new_width, 500))  # Resize with fixed height of 500px

            # ✅ Fix: Ensure cross-platform compatibility for file paths
            save_dir = get_path("captured_images")  # Convert to absolute, OS-correct path
            save_dir.mkdir(parents=True, exist_ok=True)  # Ensure the directory exists

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")  # Unique timestamp
            file_path = save_dir / f"capture_{timestamp}.jpg"  # ✅ Use Path() for proper path joining

            # ✅ Convert to string before passing to OpenCV
            cv2.imwrite(str(file_path), cv2.cvtColor(frame, cv2.COLOR_RGB2BGR))

            # Convert to QPixmap
            bytes_per_line = ch * new_width
            q_img = QImage(frame.data, new_width, 500, bytes_per_line, QImage.Format.Format_RGB888)
            captured_pixmap = QPixmap.fromImage(q_img)

            # ✅ Emit captured image and file path
            self.captured.emit(captured_pixmap, str(file_path))  # Convert path to string for PySide6

            # ✅ Properly stop the webcam and close the window
            self.close_webcam()


    def close_webcam(self):
        """ Stops the webcam feed and releases resources """
        if self.timer:
            self.timer.stop()  # ✅ Stop the update timer
        if self.capture:
            self.capture.release()  # ✅ Release the webcam
        self.close()  # ✅ Close the window properly

    def go_back(self):
        """ Closes the webcam feed and returns to the main page """
        self.close_webcam() 

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Image Processing App")
        self.resize(400, 400)

        self.main_page = SelectPage()
        self.setCentralWidget(self.main_page)


# Run the application
app = QApplication(sys.argv)
window = MainWindow()
window.show()
app.exec()
