# import image_to_svg_bin
import cv2
import argparse
import numpy as np
import subprocess
import threading
import time
from pathlib import Path
import os
import glob
import shutil


def hconcat_resize_min(im_list, interpolation=cv2.INTER_CUBIC):
    h_min = min(im.shape[0] for im in im_list)
    im_list_resize = [cv2.resize(im, (int(im.shape[1] * h_min / im.shape[0]), h_min), interpolation=interpolation)
                      for im in im_list]
    return cv2.hconcat(im_list_resize)


def save_contour_as_svg(con, image, x, y):
    smallest_x_rect = x - 20
    smallest_y_rect = y - 20

    ppp = 'M '

    for i in con:
        ppp += str(i[0][0] - smallest_x_rect)
        ppp += ' '
        ppp += str(i[0][1] - smallest_y_rect)
        ppp += ' '

    ppp += str(con[0][0][0] - smallest_x_rect)
    ppp += ' '
    ppp += str(con[0][0][1] - smallest_y_rect)
    ppp += ' ' 

    # print(ppp)
    return ppp


# Nesting automation functions
def update_status(message):
    """Print status updates for the user"""
    print(f"[NESTING STATUS] {message}")

def run_svg_generation():
    """Run the SVG generation process"""
    try:
        update_status("Generating SVG from binary image...")
        result = subprocess.run(['python', 'image_to_svg_bin.py'],
                              capture_output=True, text=True, cwd=os.getcwd())
        if result.returncode == 0:
            update_status("✅ SVG generation completed successfully!")
            return True
        else:
            update_status(f"❌ SVG generation failed: {result.stderr}")
            return False
    except Exception as e:
        update_status(f"❌ Error running SVG generation: {e}")
        return False

def automated_nesting():
    """Automated nesting process using Selenium"""
    try:
        # Import selenium modules here to avoid import errors if not installed
        from selenium import webdriver
        from selenium.webdriver.common.by import By
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service

        update_status("Setting up Chrome browser...")

        # Setup Chrome options
        chrome_options = Options()
        chrome_options.add_experimental_option("detach", True)
        chrome_options.add_argument("--allow-file-access-from-files")
        chrome_options.headless = False  # Keep visible for user to see progress

        # Start Chrome
        driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)

        # Get current directory and navigate to SVGnest
        current_dir = os.getcwd()
        svgnest_path = f"file://{current_dir}/SVGnest/index.html"
        update_status("Opening SVGnest interface...")
        driver.get(svgnest_path)

        # Wait for page to load
        time.sleep(2)

        # Upload the generated SVG file
        update_status("Uploading SVG file...")
        svg_file_path = os.path.abspath("path.svg")
        if not os.path.exists(svg_file_path):
            update_status("❌ Error: path.svg not found!")
            driver.quit()
            return False

        choose_file = driver.find_element(By.ID, "fileinput")
        choose_file.send_keys(svg_file_path)

        time.sleep(2)

        # Select the bin (blue rectangle)
        update_status("Selecting bin for nesting...")
        try:
            # Look for the bin element (usually a rect element)
            bin_elements = driver.find_elements(By.CSS_SELECTOR, "rect[stroke='blue']")
            if bin_elements:
                bin_elements[0].click()
                update_status("✅ Bin selected successfully!")
            else:
                # Fallback: try to find any rect element
                rect_elements = driver.find_elements(By.TAG_NAME, "rect")
                if rect_elements:
                    rect_elements[0].click()
                    update_status("✅ Bin selected (fallback method)!")
                else:
                    update_status("⚠️ Warning: Could not auto-select bin. Please select manually.")
        except Exception as e:
            update_status(f"⚠️ Warning: Could not auto-select bin: {e}")

        time.sleep(1)

        # Start nesting
        update_status("Starting nesting algorithm...")
        start_nest_button = driver.find_element(By.ID, "start")
        start_nest_button.click()

        # Monitor nesting progress
        update_status("Monitoring nesting progress...")
        max_wait_time = 300  # 5 minutes maximum
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            try:
                # Get progress information
                material_utilization = driver.find_element(By.ID, "info_efficiency").get_attribute("innerHTML")
                iterations_count = driver.find_element(By.ID, "info_iterations").get_attribute("innerHTML")
                placed_parts = driver.find_element(By.ID, "info_placed").get_attribute("innerHTML")

                if material_utilization and iterations_count and placed_parts:
                    update_status(f"Progress - Utilization: {material_utilization}%, Iterations: {iterations_count}, Parts: {placed_parts}")

                    # Check if we have enough iterations (minimum 5)
                    if int(iterations_count) >= 5:
                        update_status("Sufficient iterations reached. Downloading result...")
                        break

            except Exception as e:
                pass  # Continue monitoring

            time.sleep(2)

        # Download the result
        update_status("Downloading nesting result...")
        download_button = driver.find_element(By.ID, "download")
        download_button.click()

        # Wait for download to complete
        downloads_path = str(Path.home() / "Downloads")
        output_file = os.path.join(downloads_path, "SVGnest-output.svg")

        wait_time = 0
        while not os.path.exists(output_file) and wait_time < 30:
            time.sleep(1)
            wait_time += 1

        if os.path.exists(output_file):
            # Move the file to our project directory
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            final_output = f"nested_result_{timestamp}.svg"
            shutil.move(output_file, final_output)
            update_status(f"✅ Nesting completed! Result saved as: {final_output}")

            # Close browser
            driver.quit()
            return True
        else:
            update_status("❌ Error: Could not download nesting result")
            driver.quit()
            return False

    except ImportError:
        update_status("❌ Error: Selenium not installed. Please install: pip install selenium webdriver-manager")
        return False
    except Exception as e:
        update_status(f"❌ Error during automated nesting: {e}")
        try:
            driver.quit()
        except:
            pass
        return False

def start_automated_nesting():
    """Start the complete automated nesting workflow"""
    update_status("🚀 Starting automated nesting workflow...")

    # Step 1: Generate SVG from binary image
    if not run_svg_generation():
        return False

    # Step 2: Run automated nesting
    if not automated_nesting():
        return False

    update_status("🎉 Automated nesting workflow completed successfully!")
    return True


def process_image():

    global c
    global x,y
    # black color boundaries [B, G, R]
    # l = 0
    # u = 100
    lower = [l, l, l]
    upper = [u, u, u]

    # create NumPy arrays from the boundaries
    lower = np.array(lower, dtype="uint8")
    upper = np.array(upper, dtype="uint8")

    # find the colors within the specified boundaries and apply
    # the mask
    global img_sized
    img_sized = cv2.resize(img, None, fx=max(img_scaling_factor,1)/100, fy=max(img_scaling_factor,1)/100)

    blur = cv2.GaussianBlur(img_sized,(GaussianBlur_size,GaussianBlur_size),0) #51
    mask = cv2.inRange(blur, lower, upper)
    output = cv2.bitwise_or(blur, blur)

    # blur = cv2.GaussianBlur(ima,(5,5),0)
    
    ret, thresh = cv2.threshold(mask, threshold_threshold, 255, 0) #40

    # cv2.imshow('img_sized', img_sized)
    # cv2.imshow('thresh', thresh)
    # cv2.imshow('output', output)

    
    # cv2.waitKey(0)

    stencil = np.zeros(img_sized.shape)  # .astype(image.dtype)
 
    if (cv2.__version__[0] > '3'):
        contours, hierarchy = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    else:
        im2, contours, hierarchy = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)


    if len(contours) != 0:
        global ok_to_save
        ok_to_save = True
        # draw in blue the contours that were founded

        # find the biggest countour (c) by the area
        c = max(contours, key=cv2.contourArea)
        x, y, w, h = cv2.boundingRect(c)
        cv2.drawContours(output, c, -1, 255, 3)
        # draw the biggest contour (c) in green
        cv2.rectangle(output, (x, y), (x + w, y + h), (0, 255, 255), 2)

        cntr_z = c
        color_z = [255, 255, 255]
        cv2.fillPoly(stencil, [cntr_z], color_z)

        cv2.circle(output, (x, y), 2, (200,255,100), 2)
        cv2.circle(output, (x+w, y), 2, (200,255,100), 2)
        cv2.circle(output, (x, y+h), 2, (200,255,100), 2)
        cv2.circle(output, (x+w, y+h), 2, (200,255,100), 2)

        # print(x,y)

        out_bin = stencil[max(y - 50, 0):y + h + 50, max(x - 50, 0):x + w + 50]
        # cv2.imshow("img", )
        # cv2.imshow("stencil", stencil)
        # cv2.imshow("out", output)
        # cv2.imshow("out_bin", out_bin)




        # cv2.imwrite("sang_bin.png", out_bin)


        img_copy = img_sized.copy()

        # if draw_contours:
        #     cv2.drawContours(img_copy, result_contours, -1, (0, 255, 0), 1)

        # if draw_boxes:
        #     for box in result_boxes:
        #         cv2.rectangle(img_copy, tuple(box[0]), tuple(box[1]), (0, 200, 100), 1)

        # if draw_boxes_with_gap:
        #     for box_with_gap in result_boxes_with_gap:
        #         cv2.rectangle(img_copy, tuple(box_with_gap[0]), tuple(box_with_gap[1]), (0, 50, 100), 2)

        # print(c)  
        cv2.drawContours(img_copy, c, -1, 255, 3)
        x,y,w,h = cv2.boundingRect(c)
        img_copy = cv2.rectangle(img_copy,(x,y),(x+w,y+h),(0,255,0),3)
        img_copy = cv2.circle(img_copy, (x,y), 5, (0,0,255))
        
        # cv2.imshow("Result", cv2.vconcat([ stencil, thresh]))
 
        # if img_copy.size != 0:
            # cv2.imshow('img_copy', img_copy)

        # cv2.imshow('out_bin', out_bin)
        
        if out_bin.size != 0:
            # cv2.imshow('out_bin', out_bin)
            ok_to_save = True
            global my_out
            my_out = out_bin
        # if stencil.size != 0:
        #     cv2.imshow('stencil', stencil)
        # if thresh.size != 0:    
        #     cv2.imshow('thresh', thresh)

        point_line_x = int(0.5*(x+w))
        point_line_y1 = y
        point_line_y2 = y+h

        font = cv2.FONT_HERSHEY_SIMPLEX
        org = (50, 50)
        fontScale = 1
        color = (0, 0, 255)
        thickness = 3
        
        img_copy = cv2.line(img_copy, (point_line_x,point_line_y1), (point_line_x, point_line_y2), (0,0,255), thickness)
        # img_copy = cv2.putText(img_copy, str(int(h*(191/504))), org, font, fontScale, color, thickness, cv2.LINE_AA)
        img_copy = cv2.putText(img_copy, str(int(h*(1/2.6))), org, font, fontScale, color, thickness, cv2.LINE_AA)

        thresh_3_channel = cv2.cvtColor(thresh, cv2.COLOR_GRAY2BGR)

        resized_img_copy = cv2.resize(img_copy, (300, 300))
        resized_thresh_3_channel = cv2.resize(thresh_3_channel, (300, 300))
        resized_stencil = cv2.resize(stencil, (300, 300))

        # cv2.imshow('Calibration guide', img_copy)

        # numpy_vertical = np.hstack((img_copy.astype('uint8'), thresh_3_channel.astype('uint8'), stencil.astype('uint8')))
        numpy_vertical = np.hstack((resized_img_copy.astype('uint8'), resized_thresh_3_channel.astype('uint8'), resized_stencil.astype('uint8')))
 
        cv2.imshow('Control', numpy_vertical)
      


def set_l(val):
    global l
    l = val
    process_image()


def set_u(val):
    global u
    u = val
    process_image()


def set_GaussianBlur_size(val):
    global GaussianBlur_size
    GaussianBlur_size = ((val // 2) * 2) + 1
    process_image()


def set_threshold_threshold(val):
    global threshold_threshold
    threshold_threshold = val
    process_image()

def set_scaling_value(val):
    global img_scaling_factor
    img_scaling_factor = val
    process_image()

def set_area_upper_limit(val):
    global area_upper_limit
    area_upper_limit = val
    process_image()

def set_area_lower_limit(val):
    global area_lower_limit
    area_lower_limit = val
    process_image()
# def set_draw_contours(val):
#     global draw_contours
#     draw_contours = val
#     process_image()


# def set_draw_boxes(val):
#     global draw_boxes
#     draw_boxes = val
#     process_image()


# def set_draw_boxes_with_gap(val):
#     global draw_boxes_with_gap
#     draw_boxes_with_gap = val
#     process_image()


parser = argparse.ArgumentParser()
img_path = "C:\\Users\\<USER>\\Desktop\\SANG\\scanner_v3\\Input_image\\2.jpg"
parser.add_argument('--img_path', required=False, type=str, default=img_path)
args = parser.parse_args()

img = cv2.imread("C:\\Users\\<USER>\\Desktop\\SANG\\scanner_v3\\Input_image\\2.jpg")


scale_percent = 30

#calculate the 50 percent of original dimensions
width = int(img.shape[1] * scale_percent / 100)
height = int(img.shape[0] * scale_percent / 100)

# dsize
dsize = (width, height)

# resize image
# img = cv2.resize(img, dsize)



l = 0  
u = 100 
GaussianBlur_size = 51  
threshold_threshold = 40  
img_scaling_factor = 50
# draw_contours = 1
# draw_boxes = 1
# draw_boxes_with_gap = 1



cv2.namedWindow('Control')
# cv2.resizeWindow("Control", 400, 400)
cv2.createTrackbar("l", "Control", 20, 255, set_l)
cv2.createTrackbar("u", "Control", 200, 255, set_u)
cv2.createTrackbar("GaussianBlur_size", "Control", 51, 101, set_GaussianBlur_size)
cv2.createTrackbar("Image_Scale", "Control", 80, 100, set_scaling_value)
cv2.createTrackbar("Area Upper Limit", "Control", 90, 100, set_area_upper_limit)
cv2.createTrackbar("Area Lower Limit", "Control", 70, 100, set_area_lower_limit)
# cv2.createTrackbar("threshold_threshold", "Control", 40, 255, set_threshold_threshold)

# cv2.createTrackbar("draw_contours", "Control", 1, 1, set_draw_contours)
# cv2.createTrackbar("draw_boxes", "Control", 1, 1, set_draw_boxes)
# cv2.createTrackbar("draw_boxes_with_gap", "Control", 1, 1, set_draw_boxes_with_gap)

# Main interaction loop
print("\n" + "="*60)
print("THRESHOLD PROCESSING CONTROLS:")
print("- Adjust trackbars to isolate your object")
print("- Press 'S' to save the binary image")
print("- Press 'N' to start automated nesting")
print("- Press 'ESC' to exit")
print("="*60 + "\n")

while True:
    k = cv2.waitKey(1) & 0xFF

    if k == 27:  # ESC key
        print("Exiting...")
        cv2.destroyAllWindows()
        break

    elif k == ord('s') or k == ord('S'):  # Save key
        if ok_to_save:
            print("Saving binary image...")
            cv2.imwrite("Input_image\\test1_bin.jpg", my_out)
            ppp = save_contour_as_svg(c, img_sized, x, y)
            print("✅ Binary image saved successfully!")
            print("💡 You can now press 'N' to start automated nesting")
        else:
            print("❌ Output is not ready for export! Please adjust the thresholds.")

    elif k == ord('n') or k == ord('N'):  # Start Nesting key
        if ok_to_save:
            print("\n" + "="*60)
            print("STARTING AUTOMATED NESTING WORKFLOW")
            print("="*60)

            # First save the binary image if not already saved
            print("Saving binary image...")
            cv2.imwrite("Input_image\\test1_bin.jpg", my_out)
            ppp = save_contour_as_svg(c, img_sized, x, y)
            print("✅ Binary image saved!")

            # Close OpenCV windows before starting automation
            cv2.destroyAllWindows()

            # Start the automated nesting workflow in a separate thread
            print("Starting automated nesting workflow...")
            nesting_thread = threading.Thread(target=start_automated_nesting)
            nesting_thread.daemon = True
            nesting_thread.start()

            # Wait for the nesting to complete
            nesting_thread.join()

            print("\n" + "="*60)
            print("NESTING WORKFLOW COMPLETED")
            print("="*60)
            break
        else:
            print("❌ Cannot start nesting: Output is not ready!")
            print("💡 Please adjust the thresholds first, then press 'S' to save.")

    # Small delay to prevent high CPU usage
    time.sleep(0.01)
