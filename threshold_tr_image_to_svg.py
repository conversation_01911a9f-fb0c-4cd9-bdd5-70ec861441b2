# import image_to_svg_bin
import cv2
import argparse
import numpy as np


def hconcat_resize_min(im_list, interpolation=cv2.INTER_CUBIC):
    h_min = min(im.shape[0] for im in im_list)
    im_list_resize = [cv2.resize(im, (int(im.shape[1] * h_min / im.shape[0]), h_min), interpolation=interpolation)
                      for im in im_list]
    return cv2.hconcat(im_list_resize)


def save_contour_as_svg(con, image, x, y):
    smallest_x_rect = x - 20
    smallest_y_rect = y - 20

    ppp = 'M '

    for i in con:
        ppp += str(i[0][0] - smallest_x_rect)
        ppp += ' '
        ppp += str(i[0][1] - smallest_y_rect)
        ppp += ' '

    ppp += str(con[0][0][0] - smallest_x_rect)
    ppp += ' '
    ppp += str(con[0][0][1] - smallest_y_rect)
    ppp += ' ' 

    # print(ppp)
    return ppp


def process_image():

    global c
    global x,y
    # black color boundaries [B, G, R]
    # l = 0
    # u = 100
    lower = [l, l, l]
    upper = [u, u, u]

    # create NumPy arrays from the boundaries
    lower = np.array(lower, dtype="uint8")
    upper = np.array(upper, dtype="uint8")

    # find the colors within the specified boundaries and apply
    # the mask
    global img_sized
    img_sized = cv2.resize(img, None, fx=max(img_scaling_factor,1)/100, fy=max(img_scaling_factor,1)/100)

    blur = cv2.GaussianBlur(img_sized,(GaussianBlur_size,GaussianBlur_size),0) #51
    mask = cv2.inRange(blur, lower, upper)
    output = cv2.bitwise_or(blur, blur)

    # blur = cv2.GaussianBlur(ima,(5,5),0)
    
    ret, thresh = cv2.threshold(mask, threshold_threshold, 255, 0) #40

    # cv2.imshow('img_sized', img_sized)
    # cv2.imshow('thresh', thresh)
    # cv2.imshow('output', output)

    
    # cv2.waitKey(0)

    stencil = np.zeros(img_sized.shape)  # .astype(image.dtype)
 
    if (cv2.__version__[0] > '3'):
        contours, hierarchy = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
    else:
        im2, contours, hierarchy = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)


    if len(contours) != 0:
        global ok_to_save
        ok_to_save = True
        # draw in blue the contours that were founded

        # find the biggest countour (c) by the area
        c = max(contours, key=cv2.contourArea)
        x, y, w, h = cv2.boundingRect(c)
        cv2.drawContours(output, c, -1, 255, 3)
        # draw the biggest contour (c) in green
        cv2.rectangle(output, (x, y), (x + w, y + h), (0, 255, 255), 2)

        cntr_z = c
        color_z = [255, 255, 255]
        cv2.fillPoly(stencil, [cntr_z], color_z)

        cv2.circle(output, (x, y), 2, (200,255,100), 2)
        cv2.circle(output, (x+w, y), 2, (200,255,100), 2)
        cv2.circle(output, (x, y+h), 2, (200,255,100), 2)
        cv2.circle(output, (x+w, y+h), 2, (200,255,100), 2)

        # print(x,y)

        out_bin = stencil[max(y - 50, 0):y + h + 50, max(x - 50, 0):x + w + 50]
        # cv2.imshow("img", )
        # cv2.imshow("stencil", stencil)
        # cv2.imshow("out", output)
        # cv2.imshow("out_bin", out_bin)




        # cv2.imwrite("sang_bin.png", out_bin)


        img_copy = img_sized.copy()

        # if draw_contours:
        #     cv2.drawContours(img_copy, result_contours, -1, (0, 255, 0), 1)

        # if draw_boxes:
        #     for box in result_boxes:
        #         cv2.rectangle(img_copy, tuple(box[0]), tuple(box[1]), (0, 200, 100), 1)

        # if draw_boxes_with_gap:
        #     for box_with_gap in result_boxes_with_gap:
        #         cv2.rectangle(img_copy, tuple(box_with_gap[0]), tuple(box_with_gap[1]), (0, 50, 100), 2)

        # print(c)  
        cv2.drawContours(img_copy, c, -1, 255, 3)
        x,y,w,h = cv2.boundingRect(c)
        img_copy = cv2.rectangle(img_copy,(x,y),(x+w,y+h),(0,255,0),3)
        img_copy = cv2.circle(img_copy, (x,y), 5, (0,0,255))
        
        # cv2.imshow("Result", cv2.vconcat([ stencil, thresh]))
 
        # if img_copy.size != 0:
            # cv2.imshow('img_copy', img_copy)

        # cv2.imshow('out_bin', out_bin)
        
        if out_bin.size != 0:
            # cv2.imshow('out_bin', out_bin)
            ok_to_save = True
            global my_out
            my_out = out_bin
        # if stencil.size != 0:
        #     cv2.imshow('stencil', stencil)
        # if thresh.size != 0:    
        #     cv2.imshow('thresh', thresh)

        point_line_x = int(0.5*(x+w))
        point_line_y1 = y
        point_line_y2 = y+h

        font = cv2.FONT_HERSHEY_SIMPLEX
        org = (50, 50)
        fontScale = 1
        color = (0, 0, 255)
        thickness = 3
        
        img_copy = cv2.line(img_copy, (point_line_x,point_line_y1), (point_line_x, point_line_y2), (0,0,255), thickness)
        # img_copy = cv2.putText(img_copy, str(int(h*(191/504))), org, font, fontScale, color, thickness, cv2.LINE_AA)
        img_copy = cv2.putText(img_copy, str(int(h*(1/2.6))), org, font, fontScale, color, thickness, cv2.LINE_AA)

        thresh_3_channel = cv2.cvtColor(thresh, cv2.COLOR_GRAY2BGR)

        resized_img_copy = cv2.resize(img_copy, (300, 300))
        resized_thresh_3_channel = cv2.resize(thresh_3_channel, (300, 300))
        resized_stencil = cv2.resize(stencil, (300, 300))

        # cv2.imshow('Calibration guide', img_copy)

        # numpy_vertical = np.hstack((img_copy.astype('uint8'), thresh_3_channel.astype('uint8'), stencil.astype('uint8')))
        numpy_vertical = np.hstack((resized_img_copy.astype('uint8'), resized_thresh_3_channel.astype('uint8'), resized_stencil.astype('uint8')))
 
        cv2.imshow('Control', numpy_vertical)
      


def set_l(val):
    global l
    l = val
    process_image()


def set_u(val):
    global u
    u = val
    process_image()


def set_GaussianBlur_size(val):
    global GaussianBlur_size
    GaussianBlur_size = ((val // 2) * 2) + 1
    process_image()


def set_threshold_threshold(val):
    global threshold_threshold
    threshold_threshold = val
    process_image()

def set_scaling_value(val):
    global img_scaling_factor
    img_scaling_factor = val
    process_image()

def set_area_upper_limit(val):
    global area_upper_limit
    area_upper_limit = val
    process_image()

def set_area_lower_limit(val):
    global area_lower_limit
    area_lower_limit = val
    process_image()
# def set_draw_contours(val):
#     global draw_contours
#     draw_contours = val
#     process_image()


# def set_draw_boxes(val):
#     global draw_boxes
#     draw_boxes = val
#     process_image()


# def set_draw_boxes_with_gap(val):
#     global draw_boxes_with_gap
#     draw_boxes_with_gap = val
#     process_image()


parser = argparse.ArgumentParser()
img_path = "C:\\Users\\<USER>\\Desktop\\SANG\\scanner_v3\\Input_image\\2.jpg"
parser.add_argument('--img_path', required=False, type=str, default=img_path)
args = parser.parse_args()

img = cv2.imread('Input_image\\2.jpg')


scale_percent = 30

#calculate the 50 percent of original dimensions
width = int(img.shape[1] * scale_percent / 100)
height = int(img.shape[0] * scale_percent / 100)

# dsize
dsize = (width, height)

# resize image
# img = cv2.resize(img, dsize)



l = 0  
u = 100 
GaussianBlur_size = 51  
threshold_threshold = 40  
img_scaling_factor = 50
# draw_contours = 1
# draw_boxes = 1
# draw_boxes_with_gap = 1



cv2.namedWindow('Control')
# cv2.resizeWindow("Control", 400, 400)
cv2.createTrackbar("l", "Control", 20, 255, set_l)
cv2.createTrackbar("u", "Control", 200, 255, set_u)
cv2.createTrackbar("GaussianBlur_size", "Control", 51, 101, set_GaussianBlur_size)
cv2.createTrackbar("Image_Scale", "Control", 80, 100, set_scaling_value)
cv2.createTrackbar("Area Upper Limit", "Control", 90, 100, set_area_upper_limit)
cv2.createTrackbar("Area Lower Limit", "Control", 70, 100, set_area_lower_limit)
# cv2.createTrackbar("threshold_threshold", "Control", 40, 255, set_threshold_threshold)

# cv2.createTrackbar("draw_contours", "Control", 1, 1, set_draw_contours)
# cv2.createTrackbar("draw_boxes", "Control", 1, 1, set_draw_boxes)
# cv2.createTrackbar("draw_boxes_with_gap", "Control", 1, 1, set_draw_boxes_with_gap)

k = cv2.waitKey(0)

if  k == 27:
    cv2.destroyAllWindows()

else:
    if ok_to_save:
        print("saved!")
        cv2.imwrite("Input_image\\test1_bin.jpg", my_out)
        ppp = save_contour_as_svg(c, img_sized, x, y)
        cv2.destroyAllWindows()
    else:
        print("output is not ok for export!")
        cv2.destroyAllWindows()
