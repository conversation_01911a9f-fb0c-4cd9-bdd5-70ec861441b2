from __future__ import print_function
from __future__ import division
from builtins import range
from past.utils import old_div
import numpy as np
import cv2

def get_bin_from_image(image, GaussianBlur_size, threshold_threshold):
    # Convert image to grayscale for simpler thresholding
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Apply Gaussian Blur
    blur = cv2.GaussianBlur(gray, (GaussianBlur_size, GaussianBlur_size), 0)

    # Apply thresholding to create a binary image.
    ret, thresh = cv2.threshold(blur, threshold_threshold, 255, cv2.THRESH_BINARY)
    
    # Find contours.
    if (cv2.__version__[0] > '3'):
        contours, hierarchy = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    else:
        im2, contours, hierarchy = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    c = None
    if len(contours) != 0:
        # Find the biggest contour by area
        c = max(contours, key=cv2.contourArea)
    else:
        print("No contours found. Try adjusting the threshold_threshold parameter.")

    return c

def save_contour_as_svg_file(c, width, height, filename='path.svg'):
    """
    Saves the contour data to a valid SVG file.
    This version builds the path data string first to ensure correct formatting.
    """
    if c is None or len(c) == 0:
        print("No contour data to save as SVG.")
        return

    # 1. Start building the SVG path data string
    path_data = []
    start_point = c[0][0]
    path_data.append(f"M {start_point[0]} {start_point[1]}")
    for point in c[1:]:
        coord = point[0]
        path_data.append(f"L {coord[0]} {coord[1]}")
    path_data.append("Z")

    # 2. Join all the path commands into a single string
    path_string = " ".join(path_data)

    # 3. Write the complete SVG file content
    with open(filename, 'w') as f:
        f.write(f'<svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg">\n')
        f.write(f'  <path d="{path_string}" fill="white" stroke="black" />\n')
        f.write('</svg>')
        
    print(f"✅ Successfully saved contour to {filename}")

# ===================================================================
# MAIN EXECUTION BLOCK
# ===================================================================

# 1. Set your image file name here
image_path = 'Input_image\\test1_bin.jpg' # <-- INPUT YOUR IMAGE FILE NAME HERE

image = cv2.imread(image_path, 1)

if image is None:
    print(f"Error: Image '{image_path}' not found. Check the file name and path.")
else:
    height, width, channels = image.shape

    # 2. Get the original contour from the image
    original_contour = get_bin_from_image(image, GaussianBlur_size=5, threshold_threshold=127)

    # 3. If a contour was found, simplify it and save it
    if original_contour is not None:
        
        print(f"Original contour has {len(original_contour)} points.")
        
        # ==============================================================
        # NEW: SIMPLIFICATION STEP
        # ==============================================================
        # Adjust this factor to control simplification.
        # Smaller value (e.g., 0.001) = more detail, more points.
        # Larger value (e.g., 0.01) = less detail, fewer points.
        simplification_factor = 0.005
        
        perimeter = cv2.arcLength(original_contour, True)
        epsilon = simplification_factor * perimeter
        simplified_contour = cv2.approxPolyDP(original_contour, epsilon, True)
        
        print(f"Simplified contour has {len(simplified_contour)} points.")
        # ==============================================================

        # 4. Save the simplified contour to a new SVG file
        save_contour_as_svg_file(simplified_contour, width, height, filename='path_simplified.svg')