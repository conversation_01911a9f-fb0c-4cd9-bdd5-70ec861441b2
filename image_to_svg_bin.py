from __future__ import print_function
from __future__ import division
from builtins import range
from past.utils import old_div
import numpy as np
import cv2

def get_bin_from_image(image, GaussianBlur_size, threshold_threshold):
    # Convert image to grayscale for simpler thresholding
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Apply Gaussian Blur
    blur = cv2.GaussianBlur(gray, (GaussianBlur_size, GaussianBlur_size), 0)

    # Apply thresholding to create a binary image.
    # THRESH_BINARY will make bright areas white (255) and dark areas black (0).
    ret, thresh = cv2.threshold(blur, threshold_threshold, 255, cv2.THRESH_BINARY)
    
    # Find contours. RETR_EXTERNAL gets only the outermost contours.
    if (cv2.__version__[0] > '3'):
        contours, hierarchy = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    else:
        im2, contours, hierarchy = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    c = []
    if len(contours) != 0:
        # Find the biggest contour by area
        c = max(contours, key=cv2.contourArea)
    else:
        print("No contours found. Try adjusting the threshold_threshold parameter.")

    return c

def save_contour_as_svg_file(c, width, height):
    # CORRECTED CHECK: This safely checks if the contour variable is empty or invalid.
    if c is None or len(c) == 0:
        print("No contour data to save as SVG.")
        return

    # This function saves the contour to a file named path.svg
    with open('path.svg', 'w+') as f:
        f.write(f'<svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg">')
        f.write('<path d="')

        if len(c) > 0:
            # Start path with M (moveto)
            x, y = c[0][0]
            f.write(f'M{x} {y}')

            # Continue path with L (lineto)
            for i in range(1, len(c)):
                x, y = c[i][0]
                f.write(f' L{x} {y}')
        
        # Close the path with Z and add styling
        f.write(' Z" fill="white" stroke="black"/>') 
        f.write('</svg>')
    print("Successfully saved contour to path.svg")

# ===================================================================
# MAIN EXECUTION BLOCK
# ===================================================================

# 1. Set your image file name here
image_path = 'Input_image\\test1_bin.jpg' # <-- INPUT YOUR IMAGE FILE NAME HERE

image = cv2.imread(image_path, 1)

if image is None:
    print(f"Error: Image '{image_path}' not found. Check the file name and path.")
else:
    height, width, channels = image.shape

    # 2. Adjust these parameters if needed
    # GaussianBlur_size: must be an odd number (e.g., 3, 5, 7)
    # threshold_threshold: Value from 0-255. A value around 100-127 is a good starting point.
    contour = get_bin_from_image(image, GaussianBlur_size=5, threshold_threshold=127)

    # 3. If a contour was found, save it as an SVG
    if contour is not None and len(contour) > 0:
        # Optional: Uncomment the block below to see the contour before saving
        # print("Displaying found contour. Press any key to close and save.")
        # draw_image = np.zeros_like(image)
        # cv2.drawContours(draw_image, [contour], -1, (0, 255, 0), 2)
        # cv2.imshow("Extracted Contour", draw_image)
        # cv2.waitKey(0)
        # cv2.destroyAllWindows()

        save_contour_as_svg_file(contour, width, height)