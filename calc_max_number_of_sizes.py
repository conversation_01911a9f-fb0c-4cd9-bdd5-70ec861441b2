from shapely.geometry import Polygon
import os
import random
import shutil
import pandas as pd
from sqlalchemy import null
import svgwrite
import variables as var
from svgpathtools import parse_path
import threshold_tr_image_to_svg
import LimitParameters 

# factor = 3.6
factor = 2.6

# upper_limit = threshold_tr_image_to_svg.area_upper_limit
# lower_limit = threshold_tr_image_to_svg.area_lower_limit

lower_limit , upper_limit = LimitParameters.get_parameters("Input_image\\test1.jpg")
print('lower_limit::::' , lower_limit, 'upper_limit:::::', upper_limit)

print(__name__)

if __name__ == '__main__' or __name__ == 'calc_max_number_of_sizes':
    df = pd.read_excel(var.INPUT_EXCEL_DIR, sheet_name=['size', 'setting'])

    df_size = df.get('size')

    df_setting = df.get('setting')


    print("******"*10)
    print(df_size)
    print("******"*10)
    print("******"*10)
    print(df_setting.values.tolist())
    print("******"*10)

    for this_row in df_setting.values.tolist():
        print(this_row)
        max_variety = this_row[2]

    print('max_variety==================')
    print(max_variety)

    df_size['X'] = (df_size['X'] * factor).astype('int')
    df_size['Y'] = (df_size['Y'] * factor).astype('int')

    df_size['A'] = df_size['X'] * df_size['Y'] 
    df_size = df_size.sort_values(by=['A'], ascending=False)

    # df_size['Q_max'] = df_size['index']

    index_colunm_location = df_size.columns.get_loc('index')
    X_colunm_location = df_size.columns.get_loc('X')
    Y_colunm_location = df_size.columns.get_loc('Y')
    A_colunm_location = df_size.columns.get_loc('A')
    Q_max_colunm_location = df_size.columns.get_loc('Q_max')
    Price_colunm_location = df_size.columns.get_loc('Price')

    path_d = threshold_tr_image_to_svg.ppp
    path = parse_path(threshold_tr_image_to_svg.ppp)
    
    # print(path.isclosed())
    print(path.area())

    using_area = abs(int(path.area()))
    # using_area = bin_pgon.area

    print(df_size)

    # for i in range(len(df_size)):
    #     df_size['Q_max'][i] = int(using_area / df_size['A'][i])
    #     if df_size['Q_max'][i] > 0 : df_size['Q_max'][i] -= 1

    print(df_size)
    
    print("df_size:\n", df_size)

    with pd.ExcelWriter('output_excel.xlsx') as writer:
        df_size.to_excel(writer, sheet_name='size', index=False)