from pathlib import Path
import os

# The directory in which downloaded files are stored
DOWNLOADS_PATH = str(Path.home() / "Downloads")
# The directory which contains the svg files for the source shapes
SRC_SHAPES_DIR = "shapes"
# The file that contains the shapes in different sizes
SIZES_FILE = "sizes.svg"
# The directory in which output files are stored
OUTPUTS_DIR = "outputs"
# Run in silent mode
SILENT_MODE = False
# Current directory
CURRENT_DIR = os.getcwd()
# Iterations count
ITERATIONS = 5




INPUT_EXCEL_DIR = "shapes.xlsx"
PREPROCESSED_EXCEL_DIR = "output_excel.xlsx"
MERGED_SVG_FOLDER_DIR = "merged_SVG"


SINGLE_BIN_NESTED_SVGs_FOLDER = "outputs\single_bin"
MOST_EXSPENSIVE_SLAB_FOLDER = "outputs\single_bin\most_expensive"

INPUT_IMAGE_FOLDER = "Input_image"


