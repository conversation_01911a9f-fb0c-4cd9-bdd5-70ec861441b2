import numpy as np
import cv2
import random



def get_parameters(img_path):
    
    img = cv2.imread(img_path,0)
    img2=cv2.imread(img_path[:img_path.index('test')] + 'test1_bin.jpg',0)
    
    ret,thresh = cv2.threshold(img,127,255,cv2.THRESH_BINARY)
    kernel = np.ones((3,3), np.uint8)
    dilated = cv2.dilate(thresh, kernel, iterations=3)
    
    # find contours
    contours, hierarchy = cv2.findContours(dilated,cv2.RETR_EXTERNAL,cv2.CHAIN_APPROX_SIMPLE)
    
    
    epsilon = 0.02*cv2.arcLength(contours[0],True)
    approx = cv2.approxPolyDP(contours[0],epsilon,True)
    cv2.drawContours(img2, [approx], 0, (72,72,75), 3)
    area = cv2.contourArea(approx)
    
    epsilon2 = 0.1*cv2.arcLength(contours[0],True)
    approx2 = cv2.approxPolyDP(contours[0],epsilon2,True)
    cv2.drawContours(img2, [approx2], 0, (100,100,100), 3)
    area2 = cv2.contourArea(approx2)
    
    
    
    
    ratio=round(((area2/area)*(1.2))*100,0)
    if (ratio<=85):
        param1=random.randint(ratio,(ratio+5))
        param2=random.randint(ratio+10,100)
    else:
        param1=random.randint(90,95)
        param2=random.randint(97,100)
        
    print(param1,param2)
    
    # display output 
    # cv2.imshow('image',img2)
    # cv2.waitKey(0)
    # cv2.destroyAllWindows()
    
    return param1 , param2